.ic-Layout-wrapper {
  max-width: 100% !important;
}

.empty-border-box {
  content: "";
  height: 3.5rem;
  width: 0.1rem;
  background: rgb(16, 16, 16);
  display: block;
  margin-left: 5px;
  margin-right: 5px;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Balsamiq Sans', cursive;
  width: 100%;
  overflow-x: hidden;
}

[data-react-class]
  {height: 100% !important;}

.container-with-bg {
  background-color: #DFEBFB !important;
}


.css-1j1gydm-view-panel__content {
  background-color: transparent !important;
}

.full-window {
  height: 100% !important;
  min-height: 100vh;
  position: absolute;
  left: 0;
  width: 100%;
  background-color: lightblue;
}

#theme-selection {
  .selected-icon-border {
    border-width: 2px;
    border-style: solid;
    border-color: #1548E6;
    border-radius: 12px;
    display: inline-flex;
    padding: 0;
    margin: 0;
    line-height: 0;
    overflow: hidden;
  }
}

/* Theme System Styles */
.white-text {
  color: white !important;
  fill: white !important;
}

.theme-space .header-text {
  color: #D1D1D1 !important;
  fill: #D1D1D1 !important;
}

.theme-elements-wrapper img {
  max-width: 700px;
}

/* Dashboard Theme Background Colors */
.dashboard-theme-winter {
  background-color: #76bde9 !important;
}

.dashboard-theme-forest {
  background-color: #d9f1fd !important;
}

.dashboard-theme-space {
  background-color: #0e1d30 !important;
}

.dashboard-theme-no_theme {
  background-color: #F5F5F5 !important;
}

/* Bubble Message Container */
.renderBubbleMessage {
  position: absolute;
  right: 0;
  top: 200px;
  z-index: 10;
}

/* Bubble Message Component */
.bubble-message {
  position: absolute;
  background: #fff;
  border: 1px solid #000;
  border-radius: 6px !important;
  padding: 12px 15px;
  text-align: left;
  line-height: 19px;
  max-width: 168px !important;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* Accessibility improvements */
  word-wrap: break-word;
  hyphens: auto;
  /* Animation for smooth appearance */
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* Responsive bubble message */
@media (max-width: 768px) {
  .bubble-message {
    max-width: 140px;
    font-size: 14px;
    padding: 10px 12px;
  }

  .renderBubbleMessage {
    right: 10px;
    top: 150px;
  }
}

/* Bubble arrow styling */
.bubble-message::before,
.bubble-message::after {
  top: 100%;
  left: 20px;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

/* Inner arrow (white) */
.bubble-message::after {
  border-color: rgba(255, 255, 255, 0);
  border-top-color: #fff;
  border-width: 7px;
  margin-left: -7px;
}

/* Outer arrow (border) */
.bubble-message::before {
  border-color: rgba(0, 0, 0, 0);
  border-top-color: #000;
  border-width: 8px;
  margin-left: -8px;
}

/* Agenda View Styles */
.agenda-tab-content {
  z-index: 120;
}

#agenda_view div[role="tabpanel"] {
  box-shadow: 0px 2px 8px rgba(218, 11, 11, 0.18);
  overflow: hidden;
  border: 1px #FFFFFF;
  background-color: #FFFFFF;
  border-radius: 0px 6px 6px 6px;
}

/* Progress Bar Component */
#progress-bar {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

#completion-art {
  position: absolute;
  right: 87px;
  z-index: 5;
  /* Ensure completion art doesn't interfere with interactions */
  pointer-events: none;
}

/* Progress bar gradient styling */
#progress-bar-value span span span {
  background: linear-gradient(to right, #1548E6, #62C7FF 100%);
  border-radius: 4px;
  transition: all 0.3s ease-in-out;
}

/* Responsive progress bar */
@media (max-width: 768px) {
  #completion-art {
    right: 60px;
    /* Scale down on mobile */
    transform: scale(0.8);
  }

  #progress-bar {
    gap: 4px;
  }
}

.fireworks-gif-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 50vw;
  height: 100vh;
  z-index: 2;
  pointer-events: none;
  overflow: hidden;
}

.fireworks-gif {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-full-overlay {
  position: fixed;
  inset: 0;
  z-index: 9999;
  pointer-events: auto;
}

.overlay-blur {
  position: absolute;
  inset: 0;
  backdrop-filter: blur(10px);
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.centered-video {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 3;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
}
