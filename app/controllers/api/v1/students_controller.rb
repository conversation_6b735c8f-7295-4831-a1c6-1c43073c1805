# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  skip_authorization_check
  before_action :set_user, except: [:mark_event_complete]

  # show all student courses list
  def courses
    @records = QueryBuilder::StudentCourses.across_shard(@user)
    @default_course_image = GlobalSetting.where(setting_type: 'default_course_image').last
  end

  def course_progress
    @course_progress = QueryBuilder::StudentCourseProgress.from_valid_shard(@user, { canvas_course_id: params[:course_id], course_shard_id: params[:org_shard_id], filters: params[:filters] })

    render json: @course_progress
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def course_date_ranges
    @record = @user.course_date_ranges[0]
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def due_assignments
    filters = {
      due_date: params[:due_date],
      only_past_dues: params[:only_past_dues]
    }.compact_blank

    @assignments = QueryBuilder::StudentDueAssignments.across_shard(@user, filters)
    @calendar_events = calendar_events(params[:due_date])
    @show_fireworks = false

    return unless params[:due_date].present?

    assignments_present = @assignments.present?
    events = @calendar_events.values.flatten
    events_present = events.any?

    all_assignments_completed = !assignments_present || @assignments.all? { |a| a['requirement_status'] == 'completed' }
    all_events_completed = !events_present || events.all? { |e| e[:completed_at].present? }

    return unless all_assignments_completed && all_events_completed && (assignments_present || events_present)

    @show_fireworks = true
    create_user_daily_celebration_event(params[:due_date])
  end

  def weekly_due_assignments
    # Fetch all module requirements (assignments and non-assignments) from all shards for the student
    assignments = QueryBuilder::StudentDueAssignments.across_shard(@user, { due_date: params[:due_date], weekly: true })
    # Set week date range (Monday to Friday)
    date = Time.zone.parse(params[:due_date])
    @start_date = date.beginning_of_week
    @end_date = @start_date + 4.days

    @records = assignments.group_by do |item|
      due_at = item['submission_due_at'] || item['assignment_due_at'] || item['todo_date']
      Time.zone.parse(due_at.to_s).strftime('%Y-%m-%d') if due_at
    end
    @calendar_events = calendar_events(params[:due_date], weekly: true)
  end

  def calendar_events(due_date = nil, weekly: false)
    return {} unless due_date

    date_range = build_date_range(due_date, weekly)
    all_events = collect_events_from_shards(date_range)
    all_events.uniq! { |e| e[:sharded_canvas_id] }
    all_events.group_by { |event| event[:start_at].strftime('%Y-%m-%d') }
  end

  def update_student_celebration_event
    raise ArgumentError, 'Due date or course ID must be provided' unless params[:due_date].present? || params[:course_id].present?

    @user.against_shards do |u|
      if params[:due_date].present?
        pending_day_celebration_events(u.canvas_id, params[:due_date]).update_all(shown_at: Time.current)
      elsif params[:course_id].present?
        pending_course_celebration_events(u.canvas_id, params[:course_id]).update_all(shown_at: Time.current)
      end
    end
    head :ok
  rescue StandardError => e
    Rails.logger.error "Error updating celebration event: #{e.message}"
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def mark_event_complete
    with_organization(params[:organization_name]) do
      user = User.find_by!(canvas_id: params[:id])
      event = CalendarEvent.new(canvas_id: params[:event_id])

      CompletedCalendarEvent.find_or_create_by!(canvas_user_id: user.canvas_id, canvas_id: event.canvas_id)

      # Define a reusable lambda function to find and mark events as complete
      # This lambda takes a relation (e.g., CalendarEvent) and a canvas_id
      # It filters events that:
      #   1. Have a start_at date up to the end of the current day
      #   2. Match the provided canvas_id
      # Then marks all matching events as completed by updating the completed_at timestamp

      # If the event belongs to a different organization than the current one,
      # we need to switch to that organization's tenant to update the event there
      unless event.home_organization.name == current_organization.name
        event.home_organization.switch_tenant do
          CompletedCalendarEvent.find_or_create_by!(canvas_user_id: user.primary_record.canvas_id, canvas_id: event.primary_record.canvas_id)
        end
      end

      # Calendar events are duplicated across shards for cross-shard accessibility
      # We need to update the completion status on all shards for consistency
      # This iterates through all the user's shards and updates the event on each one
      user.against_shards do |shard_user|
        CompletedCalendarEvent.find_or_create_by!(canvas_user_id: shard_user.canvas_id, canvas_id: event.sharded_canvas_id)
      end
    end

    head :ok
  end

  private

  def set_user
    @user = User.find_by(canvas_id: params[:id])
  end

  def build_date_range(due_date, weekly)
    date = Time.zone.parse(due_date)

    if weekly
      start_date = date.beginning_of_week
      start_date.beginning_of_day..(start_date + 4.days).end_of_day
    else
      date.all_day
    end
  end

  def collect_events_from_shards(date_range)
    all_events = []

    @user.against_shards do |shard_user, org|
      events = shard_user.canvas_calendar_events(date_range)
      completed_events = fetch_completed_events(shard_user, events)
      all_events += build_event_data(events, completed_events, shard_user, org)
    end

    all_events
  end

  def fetch_completed_events(shard_user, events)
    CompletedCalendarEvent
      .where(canvas_user_id: shard_user.canvas_id, canvas_id: events.map(&:canvas_id))
      .index_by(&:canvas_id)
  end

  def build_event_data(events, completed_events, shard_user, org)
    events.map do |event|
      completed = completed_events[event.canvas_id]

      {
        canvas_id: event.canvas_id.to_s,
        title: event.title,
        start_at: event.start_at,
        end_at: event.end_at,
        completed_at: completed&.created_at,
        html_url: event.html_url,
        html_path: event.html_path(org),
        canvas_user_id: shard_user.canvas_id,
        sharded_canvas_id: event.sharded_canvas_id.to_s,
        organization_name: org.name
      }
    end
  end

  def create_user_daily_celebration_event(due_date)
    @user.against_shards do |u|
      CelebrationEvent.find_or_create_by!(
        canvas_user_id: u.canvas_id,
        event_type: 'daily_plan',
        event_date: Time.zone.parse(due_date)
      )
    end
  end

  def pending_day_celebration_events(user_id, due_date)
    CelebrationEvent.where(canvas_user_id: user_id, event_type: 'daily_plan', event_date: Time.zone.parse(due_date), shown_at: nil)
  end

  def pending_course_celebration_events(user_id, course_id)
    CelebrationEvent.where(canvas_user_id: user_id, event_type: 'module_completed', canvas_course_id: course_id, shown_at: nil)
  end
end
